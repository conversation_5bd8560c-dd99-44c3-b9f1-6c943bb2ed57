import { useNormalStore } from "@/app/store/stores";
import { APIROUTES } from "@/helpers/apiRoutes";
import axios from "@/helpers/axiosInstance";
import { cn } from "@/lib/utils";
import { useQueryClient } from "@tanstack/react-query";
import { X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import Quiz from "react-quiz-component";
import { quiz1, quiz2, quiz3 } from "../quiz";
import "../exam.css";

// TODO: Exam functionality needs to be migrated to oRPC
// The current endpoints (STARTEXAM, SAVEEXAMRESULTS, GETEXAMRESULTS) are legacy placeholders
// and need proper oRPC implementation in the backend

const startExam = async (examId) => {
    try {
        await axios.post(`${APIROUTES.USER.STARTEXAM}`, { id: examId });

        return true;
    } catch (error) {
        const errorMessage = error.message || "Unknown error occurred";
        console.error(errorMessage);
        toast.error(errorMessage);
    }
};

export default function Exam({ examId, setOpen, examResults, setExamResults }) {
    const [isQuizStarted, setIsQuizStarted] = useState(false);
    const { setPreventNavigation } = useNormalStore();
    const queryClient = useQueryClient();

    const quizRef = useRef(null);
    const handleBack = () => {
        setOpen(null);
        setExamResults(null);
    };

    useEffect(() => {
        const handleQuizStart = async () => {
            console.log("Quiz started!");
            const examStarted = await startExam(examId);
            if (!examStarted) {
                handleBack();
                return;
            }
            setIsQuizStarted(true);
            setPreventNavigation(true);

            setTimeout(() => {
                setPreventNavigation(false);
            }, 126000);
        };

        const addStartButtonListener = () => {
            const startButton = document.querySelector(".startQuizBtn");
            if (startButton) {
                startButton.addEventListener("click", handleQuizStart);
            }
        };

        // We need to wait a bit for the Quiz component to render its contents
        const timeoutId = setTimeout(addStartButtonListener, 0);

        // Cleanup function to remove the event listener
        return () => {
            clearTimeout(timeoutId);
            const startButton = document.querySelector(".startQuizBtn");
            if (startButton) {
                startButton.removeEventListener("click", handleQuizStart);
            }
        };
    }, []);

    const renderCustomResultPage = (obj) => {
        if (!obj) return null;
        console.log(obj);

        const isCorrectAnswer = (question, answer) => {
            const correctAnswer = parseInt(question?.correctAnswer);
            const userAnswer = parseInt(answer);
            return userAnswer === correctAnswer;
        };

        return (
            <div className="flex w-full flex-col items-center justify-center">
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/DdXccp3.png`}
                    className="absolute top-10 left-0 h-12 w-auto max-w-16 cursor-pointer hover:brightness-125 md:top-5 lg:h-14 lg:max-w-20"
                    alt=""
                    onClick={() => handleBack()}
                />

                <div className="mt-4 font-bold font-display text-2xl text-custom-yellow">
                    Your Score: {obj.correctPoints}/{obj.totalPoints}
                </div>
                <div className="font-bold font-display text-green-500 text-xl">
                    You received {obj.correctPoints * 5} Class Points!
                </div>
                <div className="mt-5 flex w-full flex-col items-center justify-center gap-2 rounded-md bg-gray-800 py-1 text-sm lg:w-[600px] lg:px-6">
                    {obj?.questions?.map((question, i) => {
                        return (
                            <div key={i} className="flex flex-col gap-2">
                                <hr className={cn("w-full border-gray-600/50 lg:w-[600px]", i === 0 && "border-0")} />
                                <div className="text-left! px-5 font-bold font-display text-base text-sky-500">
                                    {question.question}
                                </div>

                                {isCorrectAnswer(question, obj.userInput?.[i]) ? (
                                    <div className="flex flex-col items-center justify-center gap-0.5">
                                        <div className="flex items-center gap-1 rounded-md border border-gray-600/50 bg-slate-900 px-2 font-display text-base text-green-500">
                                            {question.answers?.[parseInt(question?.correctAnswer) - 1]}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center gap-0.5">
                                        <div className="flex items-center gap-1 rounded-md border border-gray-600/50 bg-slate-900 px-2 font-display text-base text-red-500">
                                            {question.answers?.[parseInt(obj.userInput?.[i] - 1)]}
                                            <X className="h-4 w-auto text-red-500" />
                                        </div>
                                        <div className="font-display text-base text-green-600">
                                            {question.answers?.[parseInt(question?.correctAnswer) - 1]}
                                        </div>
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    };

    const saveResults = async (results) => {
        const jsonPostBody = JSON.stringify({
            id: examId,
            results,
        });
        try {
            const response = await axios.post(`${APIROUTES.USER.SAVEEXAMRESULTS}`, jsonPostBody);

            setPreventNavigation(false);
            queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.GETEXAMRESULTS + examId,
            });
            queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.CURRENTUSERINFO,
            });
            return response;
        } catch (error) {
            const errorMessage = error.message || "Unknown error occurred";
            console.error(errorMessage);
            toast.error(errorMessage);
        }
    };

    if (examResults) return renderCustomResultPage(examResults);

    const getQuiz = (id) => {
        switch (id) {
            case 1:
                return { currentQuiz: quiz1, timer: 80 };
            case 2:
                return { currentQuiz: quiz2, timer: 125 };
            case 3:
                return { currentQuiz: quiz3, timer: 125 };
            default:
                return { currentQuiz: quiz1, timer: 80 };
        }
    };
    const { currentQuiz, timer } = getQuiz(examId);

    return (
        <div className="relative mx-auto flex w-full flex-col gap-2 rounded-md border border-gray-700/50 bg-black/20 font-body text-stroke-sm">
            <img
                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/DdXccp3.png`}
                alt=""
                className={cn(
                    "absolute top-5 left-0 h-12 w-auto cursor-pointer hover:brightness-125 lg:h-14",
                    isQuizStarted && "hidden"
                )}
                onClick={() => handleBack()}
            />
            <p
                className={cn(
                    "-bottom-12 lg:-translate-x-1/2 absolute text-center text-amber-500 text-bold text-display text-sm lg:left-1/2 lg:w-full",
                    isQuizStarted && "hidden"
                )}
            >
                NOTE: YOU CANNOT CHANGE YOUR ANSWER AFTER SELECTING ONE
            </p>

            <div
                className={cn(
                    "px-4 lg:flex lg:flex-col lg:items-center lg:justify-center 2xl:text-lg",
                    !isQuizStarted && "mt-16 lg:mt-0"
                )}
            >
                <Quiz
                    ref={quizRef}
                    shuffle
                    shuffleAnswer
                    enableProgressBar
                    quiz={currentQuiz}
                    showDefaultResult={false}
                    timer={timer}
                    customResultPage={renderCustomResultPage}
                    onComplete={(e) => {
                        saveResults(e);
                    }}
                />
            </div>
        </div>
    );
}

import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useActivateItem = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        orpc.user.useItem.mutationOptions({
            onSuccess: async (data) => {
                await queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.INVENTORY,
                });
                await queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                await queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.STATUSEFFECTS,
                });
                toast.success("Item used successfully!");
            },
            onError: (error) => {
                console.log(error?.message || "An error occurred");
                toast.error(`Error: ${error.message}`);
            },
        })
    );

    return {
        activateItem: (item, currentUser) => {
            // Validation checks before making the request
            if (item.health > 0) {
                if (currentUser.currentHealth === currentUser.health) {
                    toast.error("You're already full hp!");
                    return;
                }
            }
            if (currentUser.jailedUntil > 0 || currentUser.hospitalisedUntil > 0) {
                toast.error("You can't use this in your current state!");
                return;
            }

            // Call the mutation with the userItemId
            mutation.mutate({ userItemId: item.id });
        },
        isLoading: mutation.isPending,
    };
};

export default useActivateItem;

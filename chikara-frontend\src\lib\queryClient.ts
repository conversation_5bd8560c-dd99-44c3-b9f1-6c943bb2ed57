import { MaintenanceModeError, UnauthorizedError } from "@/helpers/apiError";
import { handleGet } from "@/helpers/axiosInstance";
import handleLogout from "@/helpers/handleLogout";
import { QueryClient, type QueryFunction } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

type QueryKeyType = [string, ...unknown[]];

export const defaultQueryFn: QueryFunction<unknown, QueryKeyType> = async ({ queryKey }) => {
    const [url, ...params] = queryKey;

    // If there are additional params, append them as query parameters
    const queryString =
        params.length > 0 ? `?${new URLSearchParams(params[0] as Record<string, string>).toString()}` : "";
    try {
        return await handleGet<unknown>(`${url}${queryString}`);
    } catch (e) {
        handleGlobalError(e);
        return null;
    }
};

const handleGlobalError = (error: unknown) => {
    if (error instanceof UnauthorizedError) {
        handleLogout();
    } else if (error instanceof MaintenanceModeError) {
        toast.error(error.message, { duration: Number.POSITIVE_INFINITY });
    } else if (error instanceof Error) {
        console.error("Unhandled Query/Mutation Error:", error);
        toast.error(`An error occurred: ${error.message}`);
    } else {
        console.error("Unknown Query/Mutation Error:", error);
        toast.error("An unknown error occurred.");
    }
};

export const createQueryClient = () => {
    return new QueryClient({
        defaultOptions: {
            queries: {
                // queryFn: defaultQueryFn,
                meta: {
                    onError: handleGlobalError,
                },
                retry: (failureCount, error) => {
                    // Don't retry on 401 or maintenance errors
                    if (error instanceof UnauthorizedError || error instanceof MaintenanceModeError) {
                        return false;
                    }
                    return failureCount < 3;
                },
                refetchOnWindowFocus: import.meta.env.DEVELOPMENT,
                refetchOnReconnect: "always",
                staleTime: 1000,
                throwOnError: (error: Error & { response?: { status: number } }) => error.response?.status >= 500,
            },
            mutations: {
                meta: {
                    onError: handleGlobalError,
                },
            },
        },
    });
};

import { orpc } from "@/lib/orpc";

export const APIROUTES = {
    // Authentication Routes (better-auth)
    AUTH: {
        LOGIN: "/auth/login",
        REQPASSWORDRESET: "/auth/requestPasswordReset",
        PASSWORDRESET: "/auth/resetPassword",
        LOGOUT: "/auth/logout",
        // Registration Routes (ORPC)
        REGISTER: orpc.auth.register.key(),
        CHECKUSERNAME: orpc.auth.checkUsername.key(),
        COMPLETEREGISTRATION: "/registration/completeRegistration", // TODO
    },

    // Admin Routes
    ADMIN: {
        CHATBAN: "/admin/chatban",
        HIDEMESSAGE: "/admin/hide-single-message",
        UNHIDEMESSAGE: "/admin/unhide-single-message",
        DELETEMESSAGE: "/admin/delete-single-message",
        GANGINFO: "/admin/gang-info?id=",
    },

    // User Routes
    USER: {
        FACULTYLIST: orpc.user.getUserList.key(),
        CURRENTUSERINFO: orpc.user.getCurrentUserInfo.key(),
        USERINFO: orpc.user.getUserInfo.key(),
        INVENTORY: orpc.user.getInventory.key(),
        EQUIPPEDITEMS: orpc.user.getEquippedItems.key(),
        EQUIPPEDCONSUMABLES: "/user/equipped-consumables", // TODO
        EQUIPCONSUMABLE: "/user/equip-consumable", // TODO
        UNEQUIPCONSUMABLE: "/user/unequip-consumable", // TODO
        TRADEABLEINVENTORY: orpc.user.getTradeableInventory.key(),
        STATUSEFFECTS: orpc.user.getStatusEffects.key(),
        UPDATEPROFILEDETAILS: orpc.user.updateProfileDetails.key(),
        TRAIN: orpc.user.train.key(),
        EQUIP: orpc.user.equipItem.key(),
        UNEQUIP: orpc.user.unequipItem.key(),
        USEITEM: orpc.user.useItem.key(),
        GAMECONFIG: orpc.user.getGameConfig.key(),
        LINKDISCORD: orpc.user.linkDiscord.key(),
        SETLASTNEWSIDREAD: orpc.user.setLastNewsIDRead.key(),
        SKILLS: orpc.user.getSkills.key(),
        // TODO: Exam endpoints need to be implemented in oRPC
        STARTEXAM: "/user/start-exam", // Legacy endpoint - needs oRPC implementation
        SAVEEXAMRESULTS: "/user/save-exam-results", // Legacy endpoint - needs oRPC implementation
        GETEXAMRESULTS: "/user/exam-results/", // Legacy endpoint - needs oRPC implementation (note: used with + examId)
    },

    // Profile Routes
    PROFILE: {
        VIEWCOMMENTS: orpc.profileComment.getComments.key(),
        CREATECOMMENT: orpc.profileComment.postComment.key(),
    },

    // Registration Codes Routes (ORPC)
    REGISTRATION_CODES: {
        REFERRALCODELIST: orpc.auth.getReferralCodes.key(),
        CHECKCODE: orpc.auth.checkCode.key(),
    },

    // Gang Routes
    GANG: {
        GANGLIST: orpc.gang.getGangList.key(),
        GANGINFO: orpc.gang.getGangInfo.key(),
        CREATEGANG: orpc.gang.createGang.key(),
        CURRENTGANGINFO: orpc.gang.getCurrentGang.key(),
        MEMBERSHARES: orpc.gang.getMemberShares.key(),
        GANGLOGS: orpc.gang.getGangLogs.key(),
        GANGINVITELIST: orpc.gang.getInviteList.key(),
        HASGANGSIGIL: orpc.gang.hasGangSigil.key(),
        GANGINVITE: orpc.gang.inviteMember.key(),
        CURRENTINVITES: orpc.gang.getCurrentInvites.key(),
        ACCEPTINVITE: orpc.gang.acceptInvite.key(),
        DECLINEINVITE: orpc.gang.declineInvite.key(),
        ASSIGNRANK: orpc.gang.assignRank.key(),
        UPDATESHARES: orpc.gang.updatePayoutShares.key(),
        UPGRADEHIDEOUT: orpc.gang.upgradeHideout.key(),
        LEAVEGANG: orpc.gang.leaveGang.key(),
        REQUESTINVITE: orpc.gang.requestInvite.key(),
        UPDATEGANGINFO: orpc.gang.updateGangInfo.key(),
        KICKMEMBER: orpc.gang.kickMember.key(),
    },

    // Items Routes
    ITEMS: {
        GETUPGRADEITEMS: orpc.item.getUpgradeItems.key(),
        UPGRADEITEM: orpc.item.upgradeItem.key(),
        GETITEMLIST: orpc.admin.item.list.key(), // Dev only route
    },

    // Bank Routes
    BANK: {
        WITHDRAW: orpc.bank.withdraw.key(),
        DEPOSIT: orpc.bank.deposit.key(),
        TRANSFER: orpc.bank.transfer.key(),
        BANKTRANSACTIONS: orpc.bank.getBankTransactions.key(),
    },

    // Shops Routes
    SHOPS: {
        SHOPLIST: orpc.shop.shopList.key(),
        SHOPINFO: orpc.shop.shopInfo.key(),
        CREATESHOP: orpc.shop.admin.createShop.key(),
        EDITSHOP: orpc.shop.admin.updateShop.key(),
        DELETESHOP: orpc.shop.admin.deleteShop.key(),
        CREATESHOPLISTING: orpc.shop.admin.createShopListing.key(),
        EDITSHOPLISTING: orpc.shop.admin.editShopListing.key(),
        DELETESHOPLISTING: orpc.shop.admin.deleteShopListing.key(),
        PURCHASEITEM: orpc.shop.purchaseItem.key(),
        SELLITEM: orpc.shop.sellItem.key(),
        GETTRADERREP: orpc.shop.getTraderRep.key(),
    },

    // Job Routes
    JOBS: {
        JOBLIST: orpc.job.list.key(),
        CURRENTJOBINFO: orpc.job.info.key(),
        APPLYFORJOB: orpc.job.apply.key(),
        APPLYFORPROMOTION: orpc.job.promote.key(),
        GETJOBLVLREQS: orpc.job.getRequirements.key(),
        CHANGEPAYOUTTIME: orpc.job.changePayoutTime.key(),
    },

    BATTLE: {
        BATTLEBEGIN: orpc.battle.begin.key(),
        ATTACK: orpc.battle.attack.key(),
        POSTBATTLEACTION: orpc.battle.postBattleAction.key(),
        STATUS: orpc.battle.getStatus.key(),
    },

    // Chat Routes
    CHAT: {
        HISTORY: orpc.chat.getHistory.key(),
        ROOMS: orpc.chat.getRooms.key(),
    },

    // Infirmary Routes
    INFIRMARY: {
        INFIRMARYLIST: orpc.infirmary.getHospitalList.key(),
        INJUREDLIST: orpc.infirmary.getInjuredList.key(),
        REVIVEPLAYER: orpc.infirmary.revivePlayer.key(),
        CHECKIN: orpc.infirmary.hospitalCheckIn.key(),
    },

    // Jail Routes
    JAIL: {
        JAILLIST: orpc.jail.jailList.key(),
        JAILBAIL: orpc.jail.bail.key(),
    },

    // Property Routes
    PROPERTY: {
        HOUSINGLIST: orpc.property.getHousingList.key(),
        PURCHASE: orpc.property.purchaseProperty.key(),
        SELL: orpc.property.sellProperty.key(),
        SETPRIMARY: orpc.property.setPrimaryProperty.key(),
    },

    // Crafting Routes
    CRAFTING: {
        GETQUEUE: orpc.crafting.getCraftingQueue.key(),
        RECIPELIST: orpc.crafting.getRecipes.key(),
        CREATERECIPE: orpc.crafting.createRecipe.key(),
        EDITRECIPE: orpc.crafting.editRecipe.key(),
        DELETERECIPE: orpc.crafting.deleteRecipe.key(),
        CRAFTITEM: orpc.crafting.craftItem.key(),
        COMPLETECRAFT: orpc.crafting.completeCraft.key(),
        CANCELCRAFT: orpc.crafting.cancelCraft.key(),
        ADMINLIST: orpc.crafting.getAdminRecipeList.key(),
    },

    // Private Messaging Routes
    MESSAGING: {
        HISTORY: orpc.privateMessage.getChatHistory.key(),
        UNREAD: orpc.privateMessage.getUnreadCount.key(),
        SENDMESSAGE: orpc.privateMessage.sendMessage.key(),
        READMESSAGE: orpc.privateMessage.markMessageRead.key(),
    },

    // Roguelike Routes
    ROGUELIKE: {
        CURRENTMAP: orpc.roguelike.getCurrentMap.key(),
        BEGIN: orpc.roguelike.beginRun.key(),
        ADVANCE: orpc.roguelike.advance.key(),
        ACTIVATENODE: orpc.roguelike.activateNode.key(),
        SCAVENGEOPTION: orpc.roguelike.chooseScavengeOption.key(),
    },

    // Notifications Routes
    NOTIFICATIONS: {
        HISTORY: orpc.notification.getList.key(),
        UNREAD: orpc.notification.getUnreadCount.key(),
        READ: orpc.notification.markRead.key(),
        UPDATEPUSHNOTIFICATIONSETTINGS: orpc.notification.updatePushSettings.key(),
        SAVEFCMTOKEN: orpc.notification.saveFCMToken.key(),
    },

    // leaderboards Routes
    LEADERBOARDS: {
        GETBOARDS: orpc.leaderboard.getLeaderBoards.key(),
        GETEMOTEBOARDS: orpc.leaderboard.getChatEmoteLeaderboards.key(),
    },

    // Courses/Dojo Routes
    COURSES: {
        COURSELIST: orpc.course.list.key(),
        STARTCOURSE: orpc.course.start.key(),
    },

    // Casino Routes
    CASINO: {
        SLOTS: orpc.casino.gamble.key(),
        LOTTERY: orpc.casino.getLottery.key(),
        ENTERLOTTERY: orpc.casino.enterLottery.key(),
        CHECKENTRY: orpc.casino.checkLotteryEntry.key(),
        ROULETTEBET: orpc.casino.placeBet.key(),
    },

    // Special/Unique Items Routes
    SPECIALITEMS: {
        DEATHNOTE: orpc.item.useDeathNote.key(),
        LIFENOTE: orpc.item.useLifeNote.key(),
        KOMPROMAT: orpc.item.useKompromat.key(),
        MEGAPHONE: orpc.item.useMegaphone.key(),
        DAILYCHEST: orpc.item.useDailyChest.key(),
        DAILYCHESTITEMS: orpc.item.getDailyChestItems.key(),
        REDEEM_MATERIALS_CRATE: orpc.item.useMaterialsCrate.key(),
        REDEEM_TOOLS_CRATE: orpc.item.useToolsCrate.key(),
    },

    // Pets Routes
    PETS: {
        LIST: orpc.pets.list.key(),
        FEED: orpc.pets.feed.key(),
        PLAY: orpc.pets.play.key(),
        TRAIN: orpc.pets.train.key(),
        CUSTOMIZE: orpc.pets.customize.key(),
        EVOLVE: orpc.pets.evolve.key(),
        SETACTIVE: orpc.pets.setActive.key(),
    },

    // Talents Routes
    TALENTS: {
        GETTALENTS: orpc.talents.getTalents.key(),
        GETUNLOCKEDTALENTS: orpc.talents.getUnlockedTalents.key(),
        UNLOCKTALENT: orpc.talents.unlockTalent.key(),
        EQUIPABILITY: orpc.talents.equipAbility.key(),
        UNEQUIPABILITY: orpc.talents.unequipAbility.key(),
        RESETTALENTS: orpc.talents.resetTalents.key(),
    },

    // Quests/Tasks Routes
    QUESTS: {
        GETQUESTPROGRESS: orpc.quest.getProgress.key(),
        AVAILABLEQUESTS: orpc.quest.getAvailable.key(),
        STARTQUEST: orpc.quest.start.key(),
        COMPLETEQUEST: orpc.quest.complete.key(),
        GETCOMBINEDQUESTLIST: orpc.quest.getCombinedList.key(),
        ACTIVEQUESTS: orpc.quest.getActive.key(),
        COMPLETEDQUESTS: orpc.quest.getCompleted.key(),
        HANDINITEM: orpc.quest.handInItem.key(),
    },

    // Bounty Routes
    BOUNTIES: {
        BOUNTYLIST: orpc.bounty.getBountyList.key(),
        ACTIVEBOUNTYLIST: orpc.bounty.getActiveBountyList.key(),
        PLACEBOUNTY: orpc.bounty.placeBounty.key(),
        DELETEBOUNTY: orpc.bounty.deleteBounty.key(),
    },

    // Suggestions Routes
    SUGGESTIONS: {
        SUGGESTIONLIST: orpc.suggestions.getSuggestions.key(),
        VOTEHISTORY: orpc.suggestions.getVoteHistory.key(),
        COMMENTS: orpc.suggestions.getComments.key(),
        CREATESUGGESTION: orpc.suggestions.create.key(),
        SUGGESTIONVOTE: orpc.suggestions.vote.key(),
        SUGGESTIONCOMMENT: orpc.suggestions.comment.key(),
        UPDATESUGGESTIONSTATE: orpc.suggestions.changeState.key(),
        AVAILABLEPOLLS: orpc.suggestions.getAvailablePolls.key(),
        CREATEPOLLRESPONSE: orpc.suggestions.submitPollResponse.key(),
        POLLRESULTS: orpc.suggestions.getPollResults.key(),
    },

    MISSIONS: {
        MISSIONLIST: orpc.mission.getList.key(),
        STARTMISSION: orpc.mission.start.key(),
        CANCELMISSION: orpc.mission.cancel.key(),
        CURRENTMISSION: orpc.mission.getCurrent.key(),
    },

    SHRINE: {
        DAILYGOAL: orpc.shrine.getGoal.key(),
        GETDONATIONS: orpc.shrine.getDonations.key(),
        DONATE: orpc.shrine.donate.key(),
        ACTIVESHRINEBUFF: orpc.shrine.getActiveBuff.key(),
        ISBUFFACTIVE: orpc.shrine.isBuffActive.key(),
    },

    AUCTIONS: {
        AUCTIONLIST: orpc.auction.getList.key(),
        CREATEAUCTIONLISTING: orpc.auction.createListing.key(),
        BUYOUTLISTING: orpc.auction.buyoutListing.key(),
        CANCELLISTING: orpc.auction.cancelListing.key(),
    },

    ROOFTOP: {
        NPCLIST: orpc.battle.rooftopList.key(),
        BEGIN: orpc.battle.beginRooftopBattle.key(),
    },

    SOCIAL: {
        FRIENDLIST: orpc.social.getFriends.key(),
        FRIENDREQUESTS: orpc.social.getFriendRequests.key(),
        SENDFRIENDREQUEST: orpc.social.sendFriendRequest.key(),
        RESPONDFRIENDREQUEST: orpc.social.respondToFriendRequest.key(),
        REMOVEFRIEND: orpc.social.removeFriend.key(),
        UPDATEFRIENDNOTE: orpc.social.updateFriendNote.key(),
        UPDATESTATUSMESSAGE: orpc.social.updateStatusMessage.key(),
        TOGGLEPRIVACYSETTINGS: orpc.social.updatePrivacySettings.key(),
        GETRIVALLIST: orpc.social.getRivals.key(),
        ADDRIVAL: orpc.social.addRival.key(),
        REMOVERIVAL: orpc.social.removeRival.key(),
        UPDATERIVALNOTE: orpc.social.updateRivalNote.key(),
    },

    SCAVENGING: {
        GENERATE_GRID: orpc.skills.scavenging.generateGrid.key(),
        REVEAL_CELL: orpc.skills.scavenging.revealCell.key(),
        ACTIVE_SESSION: orpc.skills.scavenging.getActiveSession.key(),
        DEV_GRID: orpc.skills.scavenging.devGrid.key(),
        END_SESSION: orpc.skills.scavenging.endSession.key(),
        RESET_GRID: orpc.skills.scavenging.resetGrid.key(),
    },

    MINING: {
        START: orpc.skills.startMining.key(),
        PROCESS_SWING: orpc.skills.processSwing.key(),
        SESSION: orpc.skills.getMiningSession.key(),
        CANCEL: orpc.skills.cancelMining.key(),
    },

    // Explore Routes
    EXPLORE: {
        MAP: orpc.explore.getMapByLocation.key(),
        INTERACT: orpc.explore.interactWithNode.key(),
    },
};
